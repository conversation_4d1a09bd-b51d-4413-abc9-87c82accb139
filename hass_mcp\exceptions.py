"""Custom exceptions for Hass-MCP."""

class HassClientError(Exception):
    """Base exception for Hass Client errors"""
    pass


class ConnectionError(HassClientError):
    """Connection related errors"""
    pass

class HassClientClosed(HassClientError):
    """Hass client closed"""
    pass

class AuthenticationError(HassClientError):
    """Authentication related errors"""
    pass


class EntityNotFoundError(HassClientError):
    """Entity not found error"""
    pass


class ServiceNotFoundError(HassClientError):
    """Service not found error"""
    pass