"""Hass MCP decorators."""
import functools
import logging
from typing import (
    Any, Callable, TypeVar,
    Optional, Awaitable,
)

import anyio

from .exceptions import (
    ConnectionError,
    HassClientError,
)

_LOGGER = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Awaitable[Any]])


def require_connection(func: F) -> F:
    """裝飾器：確認已連線"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        if not self.connected:
            raise ConnectionError("Not connected")
        return await func(self, *args, **kwargs)
    return wrapper


def log_errors(logger: Optional[logging.Logger] = None):
    """裝飾器：記錄錯誤"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            try:
                return await func(self, *args, **kwargs)
            except Exception as e:
                log = logger or _LOGGER
                log.error(f"Error in {func.__name__}: {e}")
                return {"error":str(e)}
        return wrapper
    return decorator


def with_timeout(timeout: float = 10.0):
    """裝飾器：為方法添加超時處理"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            try:
                with anyio.fail_after(timeout):
                    return await func(self, *args, **kwargs)
            except TimeoutError:
                raise TimeoutError(
                    f"{func.__name__} timeout ({timeout}s)"
                )
        return wrapper
    return decorator


def retry_on_failure(
    max_retries: int = 5,
    delay: float = 5.0,
):
    """裝飾器：失敗時重試"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return await func(self, *args, **kwargs)
                except anyio.get_cancelled_exc_class():
                    _LOGGER.warning(f"{func.__name__} cancelled")
                    raise
                except Exception as e:
                    last_exception = e
                    if attempt < (max_retries - 1):
                        _LOGGER.warning(
                            f"{func.__name__} retry {attempt + 1}: {e}"
                        )
                        await anyio.sleep(delay)

            _LOGGER.error(f"{func.__name__} all retries failed")
            raise last_exception
        return wrapper
    return decorator
