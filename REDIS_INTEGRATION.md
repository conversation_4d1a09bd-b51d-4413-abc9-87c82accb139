# Redis Integration for Home Assistant MCP Server

本文檔說明了Home Assistant MCP Server的Redis集成功能，包括實體緩存、全文搜索和實時同步。

## 功能概述

### 1. 實體緩存 (Entity Caching)
- 將所有Home Assistant實體數據緩存到Redis中
- 支持快速查詢，無需每次都訪問Home Assistant API
- 自動緩存實體的狀態、屬性和元數據

### 2. 全文搜索 (Full-Text Search)
- 使用RediSearch建立實體索引
- 支持複雜的搜索查詢語法
- 可以按實體名稱、友善名稱、域名、狀態等進行搜索

### 3. 實時同步 (Real-Time Synchronization)
- 通過WebSocket訂閱Home Assistant的狀態變更事件
- 自動更新Redis緩存中的實體數據
- 確保緩存與Home Assistant保持同步

## 安裝和配置

### 1. 安裝Redis
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# Windows (使用Docker)
docker run -d -p 6379:6379 redis:latest
```

### 2. 安裝RediSearch模組
```bash
# 如果使用Docker
docker run -d -p 6379:6379 redislabs/redisearch:latest

# 或者編譯安裝RediSearch模組
```

### 3. 環境變數設置
```bash
export REDIS_URL="redis://localhost:6379"
export HA_URL="http://your-homeassistant:8123"
export HA_TOKEN="your-long-lived-access-token"
```

## 使用方法

### 1. 基本實體查詢
```python
from hass_mcp.hass import HassManager

async def main():
    hass = HassManager(
        url="http://localhost:8123",
        token="your-token",
        redis_url="redis://localhost:6379"
    )
    
    async with hass:
        # 獲取實體狀態（優先從緩存）
        entity = await hass.get_entity_state("light.living_room")
        print(f"Light state: {entity['state']}")
```

### 2. 搜索實體
```python
# 簡單搜索
results = await hass.search_entities("kitchen")

# 高級搜索語法
results = await hass.search_entities("@domain:light @state:on")
results = await hass.search_entities("@friendly_name:living*")
results = await hass.search_entities("@device_class:motion")
```

### 3. 按域名獲取實體
```python
# 獲取所有燈光實體ID
light_entities = await hass.get_entities_by_domain("light")
print(f"Found {len(light_entities)} lights")
```

### 4. 緩存統計
```python
# 獲取緩存統計信息
stats = await hass.get_cache_stats()
print(f"Total entities: {stats['total_entities']}")
print(f"Domain counts: {stats['domain_counts']}")
```

## RediSearch查詢語法

### 基本語法
- `kitchen` - 搜索包含"kitchen"的實體
- `light AND living` - 搜索同時包含"light"和"living"的實體
- `sensor OR switch` - 搜索包含"sensor"或"switch"的實體

### 字段搜索
- `@domain:light` - 搜索domain為"light"的實體
- `@state:on` - 搜索狀態為"on"的實體
- `@friendly_name:living*` - 搜索友善名稱以"living"開頭的實體
- `@device_class:motion` - 搜索設備類別為"motion"的實體

### 組合查詢
- `@domain:sensor @state:on` - 搜索狀態為"on"的感測器
- `kitchen AND @domain:light` - 搜索廚房的燈光
- `@area_name:bedroom @device_class:switch` - 搜索臥室的開關

## 數據結構

### 實體緩存格式
```json
{
  "entity_id": "light.living_room",
  "state": "on",
  "domain": "light",
  "friendly_name": "Living Room Light",
  "device_class": "light",
  "area_name": "Living Room",
  "platform": "hue",
  "unique_id": "00:17:88:01:02:03:04:05-0b",
  "last_updated": "2025-01-01T12:00:00Z",
  "last_updated_ts": 1704110400.0,
  "attributes": "{\"brightness\": 255, \"color_mode\": \"xy\"}",
  "cached_at": "2025-01-01T12:00:01Z"
}
```

### 索引字段
- `entity_id` (TextField, weight=5.0) - 實體ID
- `friendly_name` (TextField, weight=3.0) - 友善名稱
- `state` (TextField) - 狀態
- `domain` (TagField) - 域名
- `device_class` (TagField) - 設備類別
- `area_name` (TextField) - 區域名稱
- `attributes` (TextField) - 屬性JSON字符串
- `last_updated_ts` (NumericField) - 最後更新時間戳
- `platform` (TextField) - 平台
- `unique_id` (TextField) - 唯一ID

## 性能優化

### 1. 緩存策略
- 初始連接時批量緩存所有實體
- 通過WebSocket事件實時更新緩存
- 優先從緩存讀取，減少對Home Assistant的API調用

### 2. 搜索優化
- 使用RediSearch的全文索引
- 支持分頁查詢，避免返回過多結果
- 字段權重設置，提高搜索相關性

### 3. 內存管理
- 定期清理過期的緩存數據
- 監控Redis內存使用情況
- 支持配置緩存過期時間

## 測試

運行測試腳本來驗證Redis集成：

```bash
python test_redis_integration.py
```

測試包括：
1. Redis連接測試
2. 基本緩存操作測試
3. 搜索功能測試
4. HassManager集成測試
5. 事件處理測試

## 故障排除

### 1. Redis連接問題
- 檢查Redis服務是否運行
- 驗證REDIS_URL環境變數
- 確認防火牆設置

### 2. RediSearch問題
- 確認RediSearch模組已安裝
- 檢查索引是否正確創建
- 驗證搜索語法

### 3. 同步問題
- 檢查WebSocket連接狀態
- 驗證事件訂閱是否成功
- 查看日誌中的錯誤信息

## 監控和維護

### 1. 緩存監控
```python
# 定期檢查緩存統計
stats = await hass.get_cache_stats()
print(f"Cache hit rate: {stats.get('hit_rate', 'N/A')}")
print(f"Memory usage: {stats['redis_info']['used_memory']}")
```

### 2. 日誌監控
- 監控Redis連接狀態
- 跟踪緩存更新頻率
- 記錄搜索查詢性能

### 3. 定期維護
- 清理過期的緩存數據
- 重建搜索索引（如需要）
- 備份重要的緩存數據

## 未來改進

1. **智能緩存策略** - 根據實體更新頻率調整緩存策略
2. **分佈式緩存** - 支持Redis集群部署
3. **緩存預熱** - 預測性緩存常用實體
4. **性能分析** - 詳細的性能指標和分析工具
5. **自動故障恢復** - 自動處理Redis連接中斷和恢復
