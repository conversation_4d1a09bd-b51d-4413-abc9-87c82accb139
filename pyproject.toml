[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
exclude = [
  "tests*", "docs*", "examples*", "scripts*", ".git", ".vscode", ".idea",
  "*.md", "*.toml", "*.yml", "*.yaml", "*.ini", "*.cfg",
  "**/__pycache__", "**/*.pyc", "**/*.pyo", "**/*.log"
]

[tool.hatch.build.targets.wheel]
packages = ["hass_mcp"]

[project]
name = "hass-mcp"
version = "0.2.0"
description = "A Home Assistant tool provider for LLMs using FastMCP"
requires-python = ">=3.13"
dependencies = [
    "fastmcp>=2.8.1",
    "aiohttp>=3.9.0",
    "httpx>=0.27.0",
    "python-dotenv>=1.0.0",
    "redis>=5.0.0",
    "orjson>=3.9.0"
]

[project.optional-dependencies]
test = [
    "pytest>=8.0.0",
]

[project.scripts]
hass_mcp = "hass_mcp.__main__:main"