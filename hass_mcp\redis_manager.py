"""Redis manager for Home Assistant entity caching and search"""
import logging
import json
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timezone

import redis
from redis.commands.search.field import <PERSON><PERSON>ield, TagField, NumericField
from redis.commands.search.indexDefinition import IndexDefinition, IndexType
from redis.commands.search.query import Query

from .exceptions import HassClientError

_LOGGER = logging.getLogger(__name__)


class RedisManager:
    """Redis manager for entity caching and search indexing"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", redis_db: int = 0):
        """Initialize Redis manager
        
        Args:
            redis_url: Redis connection URL
            redis_db: Redis database number
        """
        self.redis_url = redis_url
        self.redis_db = redis_db
        self._redis: Optional[redis.Redis] = None
        self._index_name = "hass_entities"
        self._entity_prefix = "entity:"
        self._domain_prefix = "domain:"
        self._metadata_key = "hass:metadata"
        
    async def connect(self):
        """Connect to Redis"""
        try:
            self._redis = redis.from_url(
                self.redis_url,
                db=self.redis_db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5
            )
            # Test connection
            self._redis.ping()
            _LOGGER.info("Connected to Redis successfully")

            # Create search index if not exists
            self._create_search_index()

        except Exception as e:
            _LOGGER.error(f"Failed to connect to Redis: {e}")
            raise HassClientError(f"Redis connection failed: {e}")
    
    async def disconnect(self):
        """Disconnect from Redis"""
        if self._redis:
            self._redis.close()
            self._redis = None
            _LOGGER.info("Disconnected from Redis")
    
    def _create_search_index(self):
        """Create RediSearch index for entities"""
        try:
            # Check if index already exists
            try:
                self._redis.ft(self._index_name).info()
                _LOGGER.info(f"Search index '{self._index_name}' already exists")
                return
            except:
                # Index doesn't exist, create it
                pass
            
            # Define schema for entity search
            schema = (
                TextField("entity_id", weight=5.0, as_name="entity_id"),
                TextField("friendly_name", weight=3.0, as_name="friendly_name"),
                TextField("state", as_name="state"),
                TagField("domain", as_name="domain"),
                TagField("device_class", as_name="device_class"),
                TextField("area_name", as_name="area_name"),
                TextField("attributes", as_name="attributes"),
                NumericField("last_updated_ts", as_name="last_updated_ts"),
                TextField("platform", as_name="platform"),
                TextField("unique_id", as_name="unique_id")
            )
            
            # Create index definition
            definition = IndexDefinition(
                prefix=[self._entity_prefix],
                index_type=IndexType.HASH
            )
            
            # Create the index
            self._redis.ft(self._index_name).create_index(
                fields=schema,
                definition=definition
            )

            _LOGGER.info(f"Created search index '{self._index_name}' successfully")

        except Exception as e:
            _LOGGER.error(f"Failed to create search index: {e}")
            raise HassClientError(f"Search index creation failed: {e}")
    
    async def cache_entity(self, entity_data: Dict[str, Any]):
        """Cache entity data in Redis
        
        Args:
            entity_data: Entity data from Home Assistant
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            entity_id = entity_data.get("entity_id")
            if not entity_id:
                _LOGGER.warning("Entity data missing entity_id")
                return
            
            # Extract domain from entity_id
            domain = entity_id.split(".")[0]
            
            # Prepare data for Redis
            cache_data = {
                "entity_id": entity_id,
                "state": entity_data.get("state", ""),
                "domain": domain,
                "friendly_name": entity_data.get("attributes", {}).get("friendly_name", entity_id),
                "device_class": entity_data.get("attributes", {}).get("device_class", ""),
                "area_name": entity_data.get("attributes", {}).get("area_name", ""),
                "platform": entity_data.get("platform", ""),
                "unique_id": entity_data.get("unique_id", ""),
                "last_updated": entity_data.get("last_updated", ""),
                "last_updated_ts": self._parse_timestamp(entity_data.get("last_updated", "")),
                "attributes": json.dumps(entity_data.get("attributes", {})),
                "cached_at": datetime.now(timezone.utc).isoformat()
            }
            
            # Store in Redis hash
            key = f"{self._entity_prefix}{entity_id}"
            self._redis.hset(key, mapping=cache_data)

            # Update domain set
            self._redis.sadd(f"{self._domain_prefix}{domain}", entity_id)

            # Update metadata
            self._update_metadata(domain)
            
            _LOGGER.debug(f"Cached entity: {entity_id}")
            
        except Exception as e:
            _LOGGER.error(f"Failed to cache entity {entity_data.get('entity_id', 'unknown')}: {e}")
    
    async def cache_entities_batch(self, entities: List[Dict[str, Any]]):
        """Cache multiple entities in a batch operation
        
        Args:
            entities: List of entity data from Home Assistant
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            pipe = self._redis.pipeline()
            domains = set()
            
            for entity_data in entities:
                entity_id = entity_data.get("entity_id")
                if not entity_id:
                    continue
                
                domain = entity_id.split(".")[0]
                domains.add(domain)
                
                # Prepare data for Redis
                cache_data = {
                    "entity_id": entity_id,
                    "state": entity_data.get("state", ""),
                    "domain": domain,
                    "friendly_name": entity_data.get("attributes", {}).get("friendly_name", entity_id),
                    "device_class": entity_data.get("attributes", {}).get("device_class", ""),
                    "area_name": entity_data.get("attributes", {}).get("area_name", ""),
                    "platform": entity_data.get("platform", ""),
                    "unique_id": entity_data.get("unique_id", ""),
                    "last_updated": entity_data.get("last_updated", ""),
                    "last_updated_ts": self._parse_timestamp(entity_data.get("last_updated", "")),
                    "attributes": json.dumps(entity_data.get("attributes", {})),
                    "cached_at": datetime.now(timezone.utc).isoformat()
                }
                
                # Add to pipeline
                key = f"{self._entity_prefix}{entity_id}"
                pipe.hset(key, mapping=cache_data)
                pipe.sadd(f"{self._domain_prefix}{domain}", entity_id)
            
            # Execute pipeline
            pipe.execute()

            # Update metadata for all domains
            for domain in domains:
                self._update_metadata(domain)
            
            _LOGGER.info(f"Cached {len(entities)} entities in batch")
            
        except Exception as e:
            _LOGGER.error(f"Failed to cache entities batch: {e}")
    
    async def get_cached_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Get cached entity data
        
        Args:
            entity_id: Entity ID to retrieve
            
        Returns:
            Cached entity data or None if not found
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            key = f"{self._entity_prefix}{entity_id}"
            data = self._redis.hgetall(key)
            
            if not data:
                return None
            
            # Parse attributes back to dict
            if data.get("attributes"):
                try:
                    data["attributes"] = json.loads(data["attributes"])
                except json.JSONDecodeError:
                    data["attributes"] = {}
            
            return data
            
        except Exception as e:
            _LOGGER.error(f"Failed to get cached entity {entity_id}: {e}")
            return None
    
    async def search_entities(self, query: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Search entities using RediSearch
        
        Args:
            query: Search query
            limit: Maximum number of results
            offset: Offset for pagination
            
        Returns:
            List of matching entities
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            # Build search query
            search_query = Query(query).paging(offset, limit)
            
            # Execute search
            result = self._redis.ft(self._index_name).search(search_query)
            
            entities = []
            for doc in result.docs:
                entity_data = dict(doc.__dict__)
                # Remove internal fields
                entity_data.pop("id", None)
                entity_data.pop("payload", None)
                
                # Parse attributes back to dict
                if entity_data.get("attributes"):
                    try:
                        entity_data["attributes"] = json.loads(entity_data["attributes"])
                    except json.JSONDecodeError:
                        entity_data["attributes"] = {}
                
                entities.append(entity_data)
            
            return entities
            
        except Exception as e:
            _LOGGER.error(f"Failed to search entities: {e}")
            return []
    
    async def get_entities_by_domain(self, domain: str) -> List[str]:
        """Get all entity IDs for a specific domain
        
        Args:
            domain: Domain name (e.g., 'light', 'sensor')
            
        Returns:
            List of entity IDs
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            entity_ids = self._redis.smembers(f"{self._domain_prefix}{domain}")
            return list(entity_ids) if entity_ids else []
            
        except Exception as e:
            _LOGGER.error(f"Failed to get entities for domain {domain}: {e}")
            return []
    
    async def remove_entity(self, entity_id: str):
        """Remove entity from cache
        
        Args:
            entity_id: Entity ID to remove
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            domain = entity_id.split(".")[0]
            
            # Remove from hash
            key = f"{self._entity_prefix}{entity_id}"
            self._redis.delete(key)

            # Remove from domain set
            self._redis.srem(f"{self._domain_prefix}{domain}", entity_id)
            
            _LOGGER.debug(f"Removed entity from cache: {entity_id}")
            
        except Exception as e:
            _LOGGER.error(f"Failed to remove entity {entity_id}: {e}")
    
    def _parse_timestamp(self, timestamp_str: str) -> float:
        """Parse timestamp string to Unix timestamp
        
        Args:
            timestamp_str: ISO format timestamp string
            
        Returns:
            Unix timestamp as float
        """
        try:
            if not timestamp_str:
                return 0.0
            dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
            return dt.timestamp()
        except Exception:
            return 0.0
    
    def _update_metadata(self, domain: str):
        """Update metadata for domain

        Args:
            domain: Domain name
        """
        try:
            count = self._redis.scard(f"{self._domain_prefix}{domain}")
            self._redis.hset(
                self._metadata_key,
                f"{domain}_count",
                count
            )
        except Exception as e:
            _LOGGER.error(f"Failed to update metadata for domain {domain}: {e}")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dictionary with cache statistics
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            # Get total entities
            total_keys = self._redis.eval(
                "return #redis.call('keys', ARGV[1])",
                0,
                f"{self._entity_prefix}*"
            )

            # Get domain counts
            metadata = self._redis.hgetall(self._metadata_key)

            # Get index info
            try:
                index_info = self._redis.ft(self._index_name).info()
                index_stats = {
                    "num_docs": index_info.get("num_docs", 0),
                    "max_doc_id": index_info.get("max_doc_id", 0),
                    "num_terms": index_info.get("num_terms", 0)
                }
            except:
                index_stats = {}

            memory_info = self._redis.info("memory")
            clients_info = self._redis.info("clients")

            return {
                "total_entities": total_keys,
                "domain_counts": metadata,
                "index_stats": index_stats,
                "redis_info": {
                    "used_memory": memory_info.get("used_memory_human", "unknown"),
                    "connected_clients": clients_info.get("connected_clients", 0)
                }
            }
            
        except Exception as e:
            _LOGGER.error(f"Failed to get cache stats: {e}")
            return {}
