#!/usr/bin/env python
"""Entry point for running Hass-MCP as a module"""

import os
import logging

from .server import create_mcp_server


def main():
    """
    Run the Hass-MCP server using FastMCP 2.8.1 standard approach.
    Supports multiple transport protocols: stdio, sse, streamable-http
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler()
        ]
    )
    
    ha_url = os.environ.get("HA_URL")
    ha_token = os.environ.get("HA_TOKEN")
    transport = os.environ.get("TRANSPORT", "stdio")

    if not ha_url:
        raise ValueError("HA_URL environment variable is required")
    if not ha_token:
        raise ValueError("HA_TOKEN environment variable is required")
   
    
    mcp_server = create_mcp_server(ha_url, ha_token)
    if transport == "sse":
        mcp_server.run(transport="sse", host="0.0.0.0", port=8000)
    elif transport == "streamable-http":
        mcp_server.run(transport="streamable-http", host="0.0.0.0", port=8000)
    else:
        mcp_server.run(transport="stdio")


if __name__ == "__main__":
    main()