FROM python:3.13.5-alpine3.22 AS base

COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/
ENV UV_SYSTEM_PYTHON=1

FROM base AS builder

WORKDIR /app

COPY . /app
RUN uv sync --locked

FROM base AS final

WORKDIR /app

COPY --from=builder /usr/local/lib/python3.13/site-packages /usr/local/lib/python3.13/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin
COPY --from=builder /app /app

EXPOSE 8000

CMD ["uv", "run", "hass_mcp"]